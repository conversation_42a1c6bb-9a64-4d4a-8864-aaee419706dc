#!/usr/bin/env python3
"""
Briefsy BOFU Content Focus Generator

This script takes already-categorized newsletter search queries and generates bottom-of-funnel
content focus angles that are SEO-heavy and naturally transition to Briefsy solutions.
"""

import csv
import json
import argparse
import requests
import time
import os
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor
import threading

# Configuration for VLLM API
VLLM_ENDPOINT = "http://**************:4100/v1/chat/completions"
VLLM_MODEL = "Qwen/Qwen3-4B-Instruct-2507"
VLLM_API_KEY = "this-is-my-key"
MAX_CONCURRENT = 5

def read_categorized_questions(filename: str) -> List[Dict]:
    """Read already categorized questions from CSV file."""
    questions = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # Handle different possible column names
            question_data = {}
            
            # Get the question/title
            if 'title' in row:
                question_data['question'] = row['title']
            elif 'original_question' in row:
                question_data['question'] = row['original_question']
            elif 'search_query' in row:
                question_data['question'] = row['search_query']
            else:
                question_data['question'] = list(row.values())[0]
            
            # Get category
            if 'category' in row:
                question_data['category'] = row['category']
            elif 'parent_category' in row:
                question_data['category'] = row['parent_category']
            else:
                question_data['category'] = ""
            
            # Get existing content focus if available
            if 'content_focus' in row:
                question_data['existing_content_focus'] = row['content_focus']
            else:
                question_data['existing_content_focus'] = ""
            
            questions.append(question_data)
    return questions

def read_business_context(filename: str) -> str:
    """Read business context from text file."""
    with open(filename, 'r', encoding='utf-8') as file:
        return file.read()

def read_categories(filename: str) -> List[str]:
    """Read categories from CSV file."""
    categories = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # Handle different possible column names
            if 'Category' in row:
                categories.append(row['Category'])
            elif 'category' in row:
                categories.append(row['category'])
            else:
                # Take first column value
                categories.append(list(row.values())[0])
    return categories

def categorize_question(question: str, categories: List[str]) -> str:
    """Use LLM to categorize a newsletter query into the best matching category."""

    # Format categories for the prompt
    category_list = []
    for i, cat in enumerate(categories, 1):
        category_list.append(f"{i}. {cat}")

    categories_text = "\n".join(category_list)

    prompt = f"""
You are a content categorization expert specializing in newsletter and content curation services. Your task is to match the given search query/title to the BEST fitting category from the provided list.

SEARCH QUERY/TITLE: "{question}"

AVAILABLE CATEGORIES:
{categories_text}

TASK: Select the single best matching category that most closely aligns with the search query's topic and user intent.

REQUIREMENTS:
- Choose only ONE category
- Base your decision on the main topic/interest area mentioned in the query
- Consider the user's search intent (discovery, comparison, problem-solving, etc.)
- If the query mentions multiple interests, choose the PRIMARY category
- If the query compares platforms or seeks alternatives, choose "Platform Comparison"
- If the query is about managing multiple newsletters or consolidation, choose "Newsletter Problems"
- If no perfect match exists, choose the closest logical fit
- For multi-interest queries (e.g., "Marketing + AI + Fitness"), choose "Multi-Interest Newsletter"

EXAMPLES:
- "Best AI Newsletter" → Technology (if that's a category) or relevant interest category
- "Substack Alternative" → Platform Comparison
- "Newsletter for Entrepreneurs" → Business & Career (or closest match)
- "Too Many Newsletter Subscriptions" → Newsletter Problems
- "Marketing + Tech Newsletter" → Multi-Interest Newsletter

Return ONLY the number of your chosen category (e.g., "15" for the 15th option).

SELECTED CATEGORY NUMBER:
"""

    response = query_vllm_api(prompt, max_tokens=50)

    # Extract the category number
    try:
        # Clean up the response to get just the number
        if "SELECTED CATEGORY NUMBER:" in response:
            category_num_str = response.split("SELECTED CATEGORY NUMBER:")[-1].strip()
        else:
            category_num_str = response.strip()

        # Extract just the number
        import re
        match = re.search(r'\d+', category_num_str)
        if match:
            category_num = int(match.group())
            if 1 <= category_num <= len(categories):
                return categories[category_num - 1]
    except (ValueError, IndexError):
        pass

    # Fallback to first category if parsing fails
    return categories[0] if categories else "General Newsletter"

def query_vllm_api(prompt: str, max_tokens: int = 800) -> str:
    """Query the VLLM API with retry logic."""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {VLLM_API_KEY}"
    }
    
    data = {
        "model": VLLM_MODEL,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.3,
        "max_tokens": max_tokens
    }
    
    max_retries = 3
    for attempt in range(max_retries):
        try:
            response = requests.post(VLLM_ENDPOINT, headers=headers, json=data, timeout=30)
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                return "Error processing request"
        except requests.exceptions.Timeout:
            if attempt < max_retries - 1:
                time.sleep(5)
                continue
            return "Error: Request timeout"
        except Exception as e:
            if attempt < max_retries - 1:
                time.sleep(3)
                continue
            return "Error connecting to VLLM"

def check_title_grammar(title: str) -> str:
    """Check and correct grammar in programmatically generated titles."""

    prompt = f"""
You are a professional editor specializing in content curation and newsletter services. Your task is to check and correct the grammar of the following title while preserving its core meaning and intent.

TITLE TO CHECK: "{title}"

REQUIREMENTS:
- Fix any grammatical errors, particularly pluralization issues
- Ensure proper article usage (a, an, the)
- Correct any awkward phrasing while maintaining the original meaning
- Keep the title concise and professional
- Do NOT change the core concept or platform names
- Do NOT add or remove key terms like newsletter types, interests, or platform names
- Only make minimal corrections needed for proper grammar and readability

Return ONLY the corrected title, nothing else.

CORRECTED TITLE:
"""

    response = query_vllm_api(prompt, max_tokens=100)

    # Clean up the response to extract just the corrected title
    if "CORRECTED TITLE:" in response:
        corrected_title = response.split("CORRECTED TITLE:")[-1].strip()
    else:
        corrected_title = response.strip()

    # Remove any quotes that might have been added
    corrected_title = corrected_title.strip('"').strip("'")

    return corrected_title

def generate_bofu_content_focus(question_data: Dict, business_context: str) -> str:
    """Generate a BOFU-focused content angle for the newsletter search query."""

    question = question_data['question']
    category = question_data['category']
    
    # Detect key characteristics of the search query
    question_lower = question.lower()
    
    # Check for competitor mentions
    competitor_platforms = {
        'substack': 'Substack',
        'morning brew': 'Morning Brew',
        'the hustle': 'The Hustle',
        'theskimm': 'TheSkimm',
        'medium': 'Medium Daily Digest',
        'feedly': 'Feedly',
        'flipboard': 'Flipboard',
        'pocket': 'Pocket',
        'google news': 'Google News',
        'apple news': 'Apple News',
        'rss': 'RSS Feeds'
    }
    
    mentioned_platforms = []
    for key, platform in competitor_platforms.items():
        if key in question_lower:
            mentioned_platforms.append(platform)
    
    # Check for multi-interest indicators
    has_multi_interest = any(indicator in question_lower for indicator in ['+', 'and', 'multiple', 'various', 'diverse'])
    
    # Check for pain points
    pain_points_mentioned = []
    pain_point_keywords = {
        'too many': 'Newsletter Overload',
        'information overload': 'Information Overload',
        'time': 'Time Management',
        'consolidate': 'Consolidation',
        'replace': 'Replacement Solution',
        'one newsletter': 'Single Newsletter Solution',
        'personalized': 'Personalization',
        'custom': 'Customization'
    }
    
    for keyword, pain_point in pain_point_keywords.items():
        if keyword in question_lower:
            pain_points_mentioned.append(pain_point)
    
    # Check for specific interests/industries
    interest_keywords = [
        'marketing', 'tech', 'ai', 'crypto', 'finance', 'fitness', 'business',
        'startup', 'entrepreneur', 'productivity', 'design', 'development',
        'health', 'wellness', 'investing', 'sales', 'career'
    ]
    
    detected_interests = [interest for interest in interest_keywords if interest in question_lower]
    
    # Check for persona indicators
    persona_keywords = {
        'professional': 'Busy Professionals',
        'entrepreneur': 'Entrepreneurs',
        'manager': 'Managers',
        'creator': 'Content Creators',
        'parent': 'Parents',
        'student': 'Students',
        'freelancer': 'Freelancers'
    }
    
    detected_personas = []
    for keyword, persona in persona_keywords.items():
        if keyword in question_lower:
            detected_personas.append(persona)
    
    # Build competitor comparison section
    competitor_section = ""
    if mentioned_platforms:
        platforms_text = ", ".join(mentioned_platforms)
        competitor_section = f"""
2. PLATFORM COMPARISON FOCUS (REQUIRED - platforms mentioned in query):
   - Specifically compare Briefsy vs. {platforms_text}
   - Highlight limitations of {platforms_text}: one-size-fits-all content, no multi-interest support, can't combine topics
   - Emphasize Briefsy's unique advantage: one newsletter that covers Marketing + AI + Fitness (or any combination)
   - Show how {platforms_text} forces users to subscribe to multiple newsletters, while Briefsy consolidates everything
   - Address the 7-agent system vs. static curation: Reddit mining, X/Twitter analysis, web scraping
   - Mention conversational setup vs. forms/templates: just chat naturally about interests
"""
    else:
        competitor_section = """
2. NEWSLETTER LANDSCAPE CONTEXT:
   - Position against the broken newsletter ecosystem: too many subscriptions, generic content
   - Emphasize the pain of managing 10-20+ newsletter subscriptions
   - Highlight how traditional newsletters force users to choose between breadth and depth
   - Show Briefsy as the solution that eliminates newsletter fatigue
"""
    
    # Build multi-interest section
    multi_interest_section = ""
    if has_multi_interest or len(detected_interests) > 1:
        multi_interest_section = f"""
3. MULTI-INTEREST POSITIONING (KEY DIFFERENTIATOR):
   - This is Briefsy's UNIQUE VALUE PROP - emphasize heavily
   - Show specific examples: "Marketing + AI + Fitness in ONE newsletter"
   - Explain how readers can combine ANY topics: "Crypto + Parenting + Career Development"
   - No other platform offers true multi-niche fusion
   - One cohesive newsletter vs. juggling multiple subscriptions
"""
    
    # Build persona section
    persona_section = ""
    if detected_personas:
        personas_text = ", ".join(detected_personas)
        persona_section = f"""
4. TARGET AUDIENCE SPECIFICITY:
   - Tailor content specifically for: {personas_text}
   - Address their specific pain points with newsletter overload
   - Show time-saving benefits: from 2 hours daily to 5-minute read
   - Highlight conversational setup: no complex configurations
"""

    prompt = f"""
You are a content strategist creating INSTRUCTIONAL research and writing briefs for Briefsy's BOFU content team.

BUSINESS CONTEXT:
{business_context}

SEARCH QUERY: "{question}"
CATEGORY: {category}
DETECTED INTERESTS: {', '.join(detected_interests) if detected_interests else 'General'}
DETECTED PERSONAS: {', '.join(detected_personas) if detected_personas else 'General Professional'}
COMPETITOR PLATFORMS MENTIONED: {', '.join(mentioned_platforms) if mentioned_platforms else 'None'}
MULTI-INTEREST QUERY: {'Yes' if has_multi_interest else 'No'}
PAIN POINTS DETECTED: {', '.join(pain_points_mentioned) if pain_points_mentioned else 'General discovery'}

TASK: Create comprehensive research and writing instructions for a BOFU article that guides readers towards choosing Briefsy as their personalized newsletter solution.

TARGET AUDIENCE: Professionals drowning in newsletter subscriptions, seeking efficient ways to stay informed

INSTRUCTIONAL CONTENT BRIEF:

1. RESEARCH INSTRUCTIONS FOR WRITERS:
   - Research current newsletter subscription habits (average person has 10-20+ subscriptions)
   - Find statistics on information overload and time wasted on content curation
   - Look up reader engagement rates for generic vs. personalized newsletters
   - Identify specific examples of multi-interest combinations readers want

{competitor_section}

{multi_interest_section}

{persona_section}

5. BRIEFSY KEY FEATURES TO HIGHLIGHT:
   - 7-Agent Intelligence System: React agents with MCP tools (Reddit, X/Twitter, web scraping, news aggregation)
   - Conversational Setup: No forms - just chat about interests in natural language
   - Multi-Niche Fusion: Combine ANY topics in one newsletter (Marketing + AI + Fitness + Parenting)
   - Style Matching: Share content examples, Briefsy matches that tone/format
   - Multilingual Support: Chat and receive content in any language
   - Smart Memory: Prevents repetition, learns from engagement
   - Free Forever: No credit card required, no premium tiers
   - Easy Updates: Change interests anytime through conversation

6. CONTENT STRUCTURE INSTRUCTIONS:
   - Start by acknowledging the reader's search intent and pain point
   - Provide objective comparison if competitors are mentioned
   - Include specific use case examples relevant to detected interests
   - Show the "before and after" of switching to Briefsy
   - Address common objections (setup time, quality concerns, customization limits)
   - End with clear CTA: "Try Briefsy - 2-minute conversational setup, free forever"

7. KEY MESSAGING POINTS:
   - ONE newsletter replaces 10-20+ subscriptions
   - First platform to truly support multi-interest newsletters
   - Conversational AI setup in 2 minutes (vs. forms and checkboxes)
   - 7 specialized agents working simultaneously across platforms
   - Saves 10+ hours per week on content curation
   - 100% free forever (emphasize no hidden costs)

8. SEO AND CONVERSION OPTIMIZATION:
   - Include comparison tables if platforms mentioned
   - Use specific examples: "Best Newsletter for Marketing + AI + Productivity"
   - Add FAQ section addressing common concerns
   - Include clear CTAs every 500 words
   - Link to conversational signup experience

Write a comprehensive research and content brief (6-8 sentences) that instructs writers on how to create a BOFU article addressing this search query, including specific research directions, comparison angles if applicable, unique Briefsy features to emphasize, and conversion-focused messaging.

CONTENT BRIEF:
"""

    response = query_vllm_api(prompt, max_tokens=800)
    
    # Clean up the response to extract just the content focus
    if "CONTENT BRIEF:" in response:
        content_focus = response.split("CONTENT BRIEF:")[-1].strip()
    else:
        content_focus = response.strip()
    
    return content_focus

def process_single_question(args):
    """Process a single question - for use with ThreadPoolExecutor."""
    idx, question_data, business_context, categories = args
    try:
        # First, check and correct the grammar of the title
        corrected_question = check_title_grammar(question_data['question'])

        # Categorize the question using LLM
        category_result = categorize_question(corrected_question, categories)

        # Update question_data with corrected title and category
        corrected_question_data = question_data.copy()
        corrected_question_data['question'] = corrected_question
        corrected_question_data['category'] = category_result

        # Generate BOFU content focus using the corrected title and category
        new_content_focus = generate_bofu_content_focus(corrected_question_data, business_context)

        result = {
            "original_question": question_data['question'],
            "corrected_question": corrected_question,
            "category": category_result,
            "content_focus": new_content_focus
        }
        return (idx, result)
    except Exception as e:
        error_result = {
            "original_question": question_data['question'],
            "corrected_question": question_data['question'],  # Use original if correction fails
            "category": question_data.get('category', 'Error'),
            "content_focus": f"Processing error: {str(e)}"
        }
        return (idx, error_result)

def process_questions_sequential_batches(questions: List[Dict], business_context: str, categories: List[str],
                                       test_mode: bool = False, concurrent: int = 5) -> List[Dict]:
    """Process questions in sequential batches with concurrent processing within each batch."""
    
    if test_mode:
        questions = questions[:20]
        print(f"Running in test mode. Processing first {len(questions)} questions...")
    else:
        print(f"Processing {len(questions)} questions with {concurrent} concurrent connections per batch...")
    
    # Create smaller batches for sequential processing
    batch_size = 500  # Process 500 questions at a time
    all_results = []
    
    for batch_start in range(0, len(questions), batch_size):
        batch_end = min(batch_start + batch_size, len(questions))
        batch_questions = questions[batch_start:batch_end]
        batch_num = (batch_start // batch_size) + 1
        total_batches = (len(questions) + batch_size - 1) // batch_size
        
        print(f"\n[PROCESSING] Batch {batch_num}/{total_batches} ({len(batch_questions)} questions)")
        
        # Prepare arguments for concurrent processing within this batch
        args_list = [
            (batch_start + i, question_data, business_context, categories)
            for i, question_data in enumerate(batch_questions)
        ]
        
        # Process this batch concurrently
        batch_results = []
        with ThreadPoolExecutor(max_workers=concurrent) as executor:
            futures = [executor.submit(process_single_question, args) for args in args_list]
            
            completed = 0
            for future in futures:
                try:
                    result = future.result()
                    batch_results.append(result)
                    completed += 1
                    
                    # Show progress every 50 questions
                    if completed % 50 == 0:
                        print(f"  [OK] {completed}/{len(batch_questions)} questions completed in batch {batch_num}")
                        
                except Exception as e:
                    print(f"  [ERROR] Error in batch {batch_num}: {e}")
        
        all_results.extend(batch_results)
        print(f"  [COMPLETE] Batch {batch_num} completed: {len(batch_results)} questions processed")
        
        # Small delay between batches to avoid overwhelming the server
        if batch_num < total_batches:
            time.sleep(2)
    
    # Sort results by original index to maintain order
    all_results.sort(key=lambda x: x[0])
    
    # Extract just the result dictionaries
    final_results = [result for _, result in all_results]
    
    return final_results

def save_results_to_csv(results: List[Dict], filename: str):
    """Save results to CSV in the requested format."""
    fieldnames = ["original_question", "corrected_question", "category", "content_focus"]

    with open(filename, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()

        for result in results:
            csv_row = {}
            for field in fieldnames:
                value = result.get(field)
                csv_row[field] = value if value is not None else ""
            writer.writerow(csv_row)

    print(f"Results saved to {filename}")

def main():
    """Main function to run the Briefsy BOFU Content Generator."""
    parser = argparse.ArgumentParser(description="Generate BOFU content focus for Briefsy newsletter search queries")
    parser.add_argument("--test", action="store_true", help="Run in test mode with first 20 questions")
    parser.add_argument("--input", default="briefsy_bofu_titles.csv",
                       help="Input CSV file with categorized newsletter search queries")
    parser.add_argument("--context", default="Briefsy_Context.txt",
                       help="Text file with business context")
    parser.add_argument("--categories", default="Briefsy_Categories.csv",
                       help="CSV file with newsletter categories")
    parser.add_argument("--output", default="briefsy_bofu_content_focus.csv",
                       help="Output CSV file")
    parser.add_argument("--concurrent", type=int, default=5,
                       help="Number of concurrent connections per batch (default: 5)")

    args = parser.parse_args()

    print("=== Briefsy BOFU Content Focus Generator ===\n")
    
    # Check if input files exist
    for file_path in [args.input, args.context, args.categories]:
        if not os.path.exists(file_path):
            print(f"Error: File '{file_path}' not found!")
            return

    try:
        # Read business context
        print("Loading business context...")
        business_context = read_business_context(args.context)

        # Read categories
        print("Loading categories...")
        categories = read_categories(args.categories)
        print(f"Loaded {len(categories)} categories")

        # Read categorized questions
        print("Loading newsletter search queries...")
        questions = read_categorized_questions(args.input)
        print(f"Loaded {len(questions)} search queries to process")

        # Process questions with sequential batch processing
        start_time = time.time()
        results = process_questions_sequential_batches(questions, business_context, categories, args.test, args.concurrent)
        end_time = time.time()
        
        # Save results
        save_results_to_csv(results, args.output)
        
        # Print summary
        processing_time = end_time - start_time
        
        print(f"\n=== SUMMARY ===")
        print(f"Total search queries processed: {len(results)}")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Average time per query: {processing_time/len(results):.2f} seconds")
        print(f"\nBOFU content focus saved to: {args.output}")
        
    except Exception as e:
        print(f"Error during processing: {e}")
        return

if __name__ == "__main__":
    main()
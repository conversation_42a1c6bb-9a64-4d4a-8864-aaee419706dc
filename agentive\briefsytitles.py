import csv
import json
from datetime import datetime

# Search qualifiers for citations
SEARCH_QUALIFIERS = [
    "Best", "Top", "Ultimate", "Complete", "Essential", 
    "Curated", "Personalized", "Custom", "Free"
]

# Core solution offerings
CORE_SOLUTIONS = [
    "AI Newsletter",
    "Personalized Newsletter",
    "AI Content Curation",
    "Custom Newsletter Service",
    "AI News Aggregator",
    "Personalized Content Feed",
    "AI Email Newsletter",
    "Curated Newsletter Platform"
]

# Interest categories & niches (what people subscribe to newsletters for)
INTEREST_CATEGORIES = {
    "Business & Career": [
        "Marketing", "Entrepreneurship", "Startups", "Leadership",
        "Sales", "Product Management", "Career Development", "Business Strategy"
    ],
    "Technology": [
        "AI", "Web Development", "Crypto", "Blockchain", "Tech News",
        "SaaS", "Cybersecurity", "Data Science", "Programming"
    ],
    "Finance & Investing": [
        "Stock Market", "Personal Finance", "Crypto Investing", 
        "Real Estate Investing", "Financial Independence", "Trading"
    ],
    "Health & Wellness": [
        "Fitness", "Nutrition", "Mental Health", "Biohacking",
        "Wellness", "Longevity", "Meditation", "Weight Loss"
    ],
    "Creative & Design": [
        "Design", "Writing", "Content Creation", "Photography",
        "UX/UI", "Graphic Design", "Creative Writing"
    ],
    "Lifestyle": [
        "Productivity", "Self-Improvement", "Travel", "Fashion",
        "Parenting", "Cooking", "Books", "Hobbies"
    ],
    "Science & Learning": [
        "Science News", "Psychology", "Philosophy", "History",
        "Education", "Research", "Academia"
    ]
}

# Competitor newsletter platforms & services
COMPETITOR_PLATFORMS = [
    "Substack", "Morning Brew", "The Hustle", "TheSkimm",
    "Medium Daily Digest", "Feedly", "Flipboard", "Pocket",
    "Google News", "Apple News", "Twitter Lists", "RSS Feeds"
]

# Pain points that Briefsy solves
PAIN_POINTS = [
    "Information Overload",
    "Too Many Newsletters",
    "Irrelevant Content",
    "Time-Consuming Research",
    "Missing Important News",
    "Scattered Sources",
    "Generic Newsletters",
    "Newsletter Fatigue"
]

# User personas (target audiences)
TARGET_PERSONAS = [
    "Busy Professionals",
    "Entrepreneurs",
    "Marketing Managers",
    "Content Creators",
    "Investors",
    "Product Managers",
    "Freelancers",
    "Small Business Owners",
    "Remote Workers",
    "Career Changers",
    "New Parents",
    "Students",
    "Executives",
    "Consultants"
]

# Newsletter types people search for
NEWSLETTER_TYPES = [
    "Daily Newsletter",
    "Weekly Newsletter",
    "Industry Newsletter",
    "Morning Newsletter",
    "News Digest",
    "Curated Newsletter",
    "Free Newsletter",
    "Email Digest"
]

def generate_titles():
    """Generate strategically distinct BOFU titles"""
    titles = set()
    
    # STRATEGY 1: Interest-Specific Newsletters
    print("Generating Strategy 1: Interest-Specific Newsletters...")
    for category, interests in INTEREST_CATEGORIES.items():
        for interest in interests:
            for qualifier in SEARCH_QUALIFIERS[:3]:
                titles.add(f"{qualifier} {interest} Newsletter")
                titles.add(f"{qualifier} {interest} Newsletter for 2025")
                titles.add(f"{qualifier} Personalized {interest} Newsletter")
            
            titles.add(f"{interest} Newsletter for Professionals")
            titles.add(f"Free {interest} Newsletter")
            titles.add(f"{interest} News Digest")
            titles.add(f"Daily {interest} Updates")
            titles.add(f"Custom {interest} Content Feed")
    
    # STRATEGY 2: Multi-Niche Combinations (Unique Briefsy Feature)
    print("Generating Strategy 2: Multi-Interest Combinations...")
    combo_examples = [
        ("Marketing", "AI", "Productivity"),
        ("Fitness", "Entrepreneurship", "Mental Health"),
        ("Crypto", "Tech News", "Investing"),
        ("Parenting", "Career Development", "Wellness"),
        ("Design", "Startups", "Creativity"),
        ("Finance", "Real Estate", "Business"),
        ("Writing", "Marketing", "Freelancing"),
        ("Programming", "AI", "Career Development")
    ]
    
    for combo in combo_examples:
        combo_str = " + ".join(combo)
        titles.add(f"Newsletter for {combo_str}")
        titles.add(f"Personalized {combo_str} Newsletter")
        titles.add(f"Best Newsletter Covering {combo_str}")
        titles.add(f"One Newsletter for {combo_str} Enthusiasts")
    
    # STRATEGY 3: Pain Point Solutions
    print("Generating Strategy 3: Pain Point Solutions...")
    for pain in PAIN_POINTS:
        titles.add(f"How to Solve {pain}")
        titles.add(f"Best Solution for {pain}")
        titles.add(f"Overcome {pain} with AI")
        titles.add(f"Stop {pain}: Personalized Newsletter Guide")
        
        for persona in TARGET_PERSONAS[:10]:
            titles.add(f"Solve {pain} for {persona}")
            titles.add(f"{persona} Guide to {pain}")
    
    # STRATEGY 4: Competitor Alternatives
    print("Generating Strategy 4: Competitor Alternatives...")
    for competitor in COMPETITOR_PLATFORMS:
        titles.add(f"Best {competitor} Alternative")
        titles.add(f"{competitor} vs. Personalized AI Newsletter")
        titles.add(f"Better Than {competitor}: AI-Powered Newsletter")
        titles.add(f"Why Switch from {competitor}")
        titles.add(f"{competitor} Alternative for Custom Content")
        
        for interest_list in list(INTEREST_CATEGORIES.values())[:4]:
            for interest in interest_list[:2]:
                titles.add(f"Best {competitor} Alternative for {interest}")
    
    # STRATEGY 5: Persona-Specific Solutions
    print("Generating Strategy 5: Persona-Specific Newsletters...")
    for persona in TARGET_PERSONAS:
        for qualifier in SEARCH_QUALIFIERS[:3]:
            titles.add(f"{qualifier} Newsletter for {persona}")
        
        titles.add(f"Newsletter for {persona} in 2025")
        titles.add(f"{persona} Newsletter Guide")
        titles.add(f"Personalized Newsletter for {persona}")
        titles.add(f"Custom Content Feed for {persona}")
        
        # Persona + Interest combinations
        for category, interests in list(INTEREST_CATEGORIES.items())[:5]:
            for interest in interests[:2]:
                titles.add(f"{interest} Newsletter for {persona}")
    
    # STRATEGY 6: Newsletter Management & Consolidation
    print("Generating Strategy 6: Newsletter Consolidation...")
    consolidation_queries = [
        "Reduce Newsletter Subscriptions",
        "One Newsletter Instead of Many",
        "Consolidate All Newsletters",
        "Replace Multiple Newsletters",
        "Too Many Newsletter Subscriptions",
        "Simplify Newsletter Subscriptions",
        "Manage Newsletter Overload"
    ]
    
    for query in consolidation_queries:
        titles.add(query)
        titles.add(f"How to {query}")
        titles.add(f"{query}: Complete Guide")
        titles.add(f"{query} with AI")
    
    # STRATEGY 7: Newsletter Discovery
    print("Generating Strategy 7: Newsletter Discovery...")
    discovery_terms = [
        "Find Best Newsletters",
        "Discover Quality Newsletters",
        "Newsletter Recommendations",
        "What Newsletters to Subscribe To",
        "Best Newsletters to Read",
        "Must-Read Newsletters"
    ]
    
    for term in discovery_terms:
        titles.add(term)
        titles.add(f"{term} in 2025")
        
        for category in INTEREST_CATEGORIES.keys():
            titles.add(f"{term} for {category}")
        
        for persona in TARGET_PERSONAS[:8]:
            titles.add(f"{term} for {persona}")
    
    # STRATEGY 8: Newsletter Features & Benefits
    print("Generating Strategy 8: Feature-Focused Titles...")
    features = [
        "AI-Powered Newsletter",
        "Personalized News Digest",
        "Custom Content Curation",
        "Smart Newsletter Service",
        "Multilingual Newsletter",
        "Free Personalized Newsletter",
        "No-Code Newsletter Setup",
        "Conversational Newsletter Setup"
    ]
    
    for feature in features:
        for qualifier in SEARCH_QUALIFIERS[:2]:
            titles.add(f"{qualifier} {feature}")
        
        titles.add(f"{feature} for 2025")
        
        for persona in TARGET_PERSONAS[:8]:
            titles.add(f"{feature} for {persona}")
    
    # STRATEGY 9: Time-Saving & Productivity
    print("Generating Strategy 9: Time-Saving Angles...")
    time_queries = [
        "Save Time Reading News",
        "Stop Wasting Time on Social Media",
        "Efficient Way to Stay Informed",
        "Quick Daily News Digest",
        "5-Minute Morning Newsletter",
        "Time-Saving News Solution",
        "Productivity Newsletter"
    ]
    
    for query in time_queries:
        titles.add(query)
        titles.add(f"Best {query}")
        titles.add(f"{query}: Complete Guide")
        
        for persona in TARGET_PERSONAS[:6]:
            titles.add(f"{query} for {persona}")
    
    # STRATEGY 10: Content Source Aggregation
    print("Generating Strategy 10: Content Source Solutions...")
    source_queries = [
        "Aggregate Reddit Content",
        "Twitter News Digest",
        "Reddit + Twitter Newsletter",
        "Multi-Source News Aggregator",
        "Combine All News Sources",
        "Reddit Discussion Newsletter",
        "Twitter Trends Newsletter"
    ]
    
    for query in source_queries:
        titles.add(query)
        titles.add(f"Best {query}")
        
        for interest_list in list(INTEREST_CATEGORIES.values())[:4]:
            for interest in interest_list[:1]:
                titles.add(f"{query} for {interest}")
    
    # STRATEGY 11: Newsletter Frequency & Timing
    print("Generating Strategy 11: Frequency & Timing...")
    for newsletter_type in NEWSLETTER_TYPES:
        for qualifier in SEARCH_QUALIFIERS[:2]:
            titles.add(f"{qualifier} {newsletter_type}")
        
        for interest_list in list(INTEREST_CATEGORIES.values())[:5]:
            for interest in interest_list[:2]:
                titles.add(f"{newsletter_type} for {interest}")
        
        for persona in TARGET_PERSONAS[:8]:
            titles.add(f"{newsletter_type} for {persona}")
    
    # STRATEGY 12: Specific Use Cases
    print("Generating Strategy 12: Use Case Scenarios...")
    use_cases = [
        "Newsletter for Career Change",
        "Newsletter for Learning AI",
        "Newsletter for Staying Current",
        "Newsletter for Industry Trends",
        "Newsletter for Professional Development",
        "Newsletter for Market Research",
        "Newsletter for Competitive Intelligence"
    ]
    
    for use_case in use_cases:
        titles.add(use_case)
        titles.add(f"Best {use_case}")
        titles.add(f"Personalized {use_case}")
    
    # STRATEGY 13: Problem-Solution Format
    print("Generating Strategy 13: Problem-Solution Titles...")
    problems = [
        "Missing Important Industry News",
        "Can't Keep Up with Multiple Topics",
        "Drowning in Unread Articles",
        "Need Better Content Filtering",
        "Want Personalized News",
        "Tired of Generic Newsletters"
    ]
    
    for problem in problems:
        titles.add(f"Solution for {problem}")
        titles.add(f"How to Stop {problem}")
        titles.add(f"Fix {problem} with AI Newsletter")
    
    # STRATEGY 14: Comparison Guides
    print("Generating Strategy 14: Comparison Content...")
    for i, comp1 in enumerate(COMPETITOR_PLATFORMS[:6]):
        for comp2 in COMPETITOR_PLATFORMS[i+1:7]:
            titles.add(f"{comp1} vs {comp2}")
            titles.add(f"{comp1} vs {comp2}: Which is Better?")
    
    # STRATEGY 15: Free & Cost-Focused
    print("Generating Strategy 15: Free/Cost Positioning...")
    for interest_list in list(INTEREST_CATEGORIES.values())[:6]:
        for interest in interest_list:
            titles.add(f"Free {interest} Newsletter")
            titles.add(f"Best Free {interest} Newsletter")
    
    for persona in TARGET_PERSONAS:
        titles.add(f"Free Newsletter for {persona}")
    
    titles.add("Best Free Personalized Newsletter")
    titles.add("Free AI Newsletter Service")
    titles.add("No Credit Card Newsletter Subscription")
    
    # STRATEGY 16: Traditional Newsletter Searches (No AI Mention)
    print("Generating Strategy 16: Traditional Newsletter Discovery...")
    
    # Simple interest-based searches (people don't know AI options exist)
    for category, interests in INTEREST_CATEGORIES.items():
        for interest in interests:
            # Basic discovery queries
            titles.add(f"{interest} Newsletter")
            titles.add(f"Subscribe to {interest} Newsletter")
            titles.add(f"{interest} Email Updates")
            titles.add(f"{interest} News Roundup")
            titles.add(f"{interest} Weekly Digest")
            titles.add(f"Get {interest} Updates")
            
            # "Looking for" phrasing
            titles.add(f"Looking for {interest} Newsletter")
            titles.add(f"Good {interest} Newsletter")
            titles.add(f"Recommended {interest} Newsletter")
            
            # Quality indicators without AI
            titles.add(f"Quality {interest} Newsletter")
            titles.add(f"Curated {interest} Content")
            titles.add(f"{interest} Content Delivered Daily")
    
    # General newsletter browsing behavior
    general_newsletter_queries = [
        "Newsletter Subscription",
        "Email Newsletter Service",
        "Sign Up for Newsletter",
        "Newsletter Recommendations",
        "Good Newsletters to Subscribe To",
        "Quality Newsletter Service",
        "Curated Email Newsletter",
        "Daily Email Digest",
        "Weekly Email Roundup",
        "Morning Newsletter Service",
        "News in My Inbox",
        "Email News Service",
        "Subscribe to Daily Updates",
        "Get Industry News by Email"
    ]
    
    for query in general_newsletter_queries:
        titles.add(query)
        titles.add(f"{query} 2025")
        
        # Add interest variations
        for interest_list in list(INTEREST_CATEGORIES.values())[:4]:
            for interest in interest_list[:2]:
                titles.add(f"{interest} {query}")
    
    # Persona + Traditional Search
    for persona in TARGET_PERSONAS:
        titles.add(f"Newsletter for {persona}")
        titles.add(f"Email Newsletter for {persona}")
        titles.add(f"Subscribe to Newsletter for {persona}")
        titles.add(f"Good Newsletter for {persona}")
    
    # Combination interests without AI mention
    traditional_combos = [
        "Business and Tech Newsletter",
        "Marketing and Sales Newsletter",
        "Fitness and Nutrition Newsletter",
        "Finance and Investing Newsletter",
        "Design and Creativity Newsletter",
        "Startup and Entrepreneurship Newsletter",
        "Productivity and Career Newsletter"
    ]
    
    for combo in traditional_combos:
        titles.add(combo)
        titles.add(f"Best {combo}")
        titles.add(f"Subscribe to {combo}")
        titles.add(f"Quality {combo}")
    
    # "I want" phrasing (high intent, traditional)
    intent_phrases = [
        "I Want Newsletter About",
        "Looking for Newsletter That Covers",
        "Need Newsletter for",
        "Where to Find Newsletter About",
        "How to Get Newsletter for"
    ]
    
    for phrase in intent_phrases:
        for interest_list in list(INTEREST_CATEGORIES.values())[:3]:
            for interest in interest_list[:2]:
                titles.add(f"{phrase} {interest}")
    
    # Multi-topic traditional searches (people wanting variety)
    titles.add("Newsletter Covering Multiple Topics")
    titles.add("Newsletter with Various Interests")
    titles.add("One Newsletter for All My Interests")
    titles.add("Newsletter That Covers Everything I Like")
    titles.add("Diverse Topic Newsletter")
    titles.add("Multi-Topic Email Digest")
    
    # Content delivery preferences (traditional)
    for interest_list in list(INTEREST_CATEGORIES.values())[:5]:
        for interest in interest_list[:1]:
            titles.add(f"Daily {interest} Email")
            titles.add(f"Weekly {interest} Update")
            titles.add(f"{interest} News Delivered to Inbox")
            titles.add(f"Subscribe to {interest} Updates")
    
    return list(titles)

def categorize_title(title):
    """Categorize titles by type"""
    title_lower = title.lower()
    
    # Check for combinations
    if "+" in title or "and" in title_lower:
        return "Multi-Interest Newsletter"
    
    # Check by interest category
    for category, interests in INTEREST_CATEGORIES.items():
        if any(interest.lower() in title_lower for interest in interests):
            return category
    
    # Check by solution type
    if any(pain.lower() in title_lower for pain in PAIN_POINTS):
        return "Pain Point Solution"
    elif any(comp.lower() in title_lower for comp in COMPETITOR_PLATFORMS) or "vs" in title_lower or "alternative" in title_lower:
        return "Platform Comparison"
    elif any(persona.lower() in title_lower for persona in TARGET_PERSONAS):
        return "Persona-Specific"
    elif "free" in title_lower or "cost" in title_lower or "price" in title_lower:
        return "Free/Cost-Focused"
    elif "time" in title_lower or "save" in title_lower or "quick" in title_lower:
        return "Time-Saving"
    elif "daily" in title_lower or "weekly" in title_lower or "morning" in title_lower:
        return "Frequency-Focused"
    else:
        return "General Newsletter"

def add_metadata(titles):
    """Add metadata to each title"""
    return [
        {
            "id": idx + 1,
            "title": title,
            "category": categorize_title(title),
            "intent": "BOFU",
            "has_comparison": "vs" in title.lower() or any(comp.lower() in title.lower() for comp in COMPETITOR_PLATFORMS),
            "is_persona_specific": any(persona.lower() in title.lower() for persona in TARGET_PERSONAS),
            "has_qualifier": any(q in title for q in SEARCH_QUALIFIERS),
            "is_multi_interest": "+" in title or ("and" in title.lower() and any(cat in title for cat in ["marketing", "fitness", "crypto"])),
            "is_free_focused": "free" in title.lower(),
            "is_ai_focused": "ai" in title.lower() or "personalized" in title.lower(),
            "mentions_time_saving": any(word in title.lower() for word in ["time", "save", "quick", "5-minute", "efficient"]),
            "is_consolidation": any(word in title.lower() for word in ["consolidate", "replace", "instead of", "one newsletter"])
        }
        for idx, title in enumerate(titles)
    ]

def export_csv(data, filename):
    """Export to CSV"""
    with open(filename, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=data[0].keys())
        writer.writeheader()
        writer.writerows(data)
    print(f"✅ Exported {len(data)} titles to {filename}")

def export_json(data, filename):
    """Export to JSON"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"✅ Exported {len(data)} titles to {filename}")

def print_statistics(data):
    """Print generation statistics"""
    print("\n" + "="*70)
    print("BRIEFSY BOFU TITLE GENERATION STATISTICS")
    print("="*70)
    print(f"Total Unique Titles: {len(data):,}")
    print(f"Titles with Platform Comparisons: {sum(1 for t in data if t['has_comparison']):,}")
    print(f"Persona-Specific Titles: {sum(1 for t in data if t['is_persona_specific']):,}")
    print(f"Multi-Interest Titles (Unique to Briefsy): {sum(1 for t in data if t['is_multi_interest']):,}")
    print(f"Free-Focused Titles: {sum(1 for t in data if t['is_free_focused']):,}")
    print(f"AI/Personalization Focused: {sum(1 for t in data if t['is_ai_focused']):,}")
    print(f"Time-Saving Angle: {sum(1 for t in data if t['mentions_time_saving']):,}")
    print(f"Newsletter Consolidation: {sum(1 for t in data if t['is_consolidation']):,}")
    print(f"Titles with Qualifiers: {sum(1 for t in data if t['has_qualifier']):,}")
    
    print("\nCategory Breakdown:")
    categories = {}
    for item in data:
        cat = item['category']
        categories[cat] = categories.get(cat, 0) + 1
    
    for cat, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
        print(f"  {cat}: {count:,}")
    
    print("\nSample Titles by Category:")
    for cat in sorted(set(item['category'] for item in data))[:10]:
        samples = [item['title'] for item in data if item['category'] == cat][:3]
        print(f"\n  {cat}:")
        for sample in samples:
            print(f"    • {sample}")
    
    print("\n" + "="*70 + "\n")

def main():
    print("📰 Briefsy Strategic BOFU Title Generator")
    print("="*70)
    print("Generating high-intent titles for personalized AI newsletter positioning\n")
    
    # Generate titles
    print("Generating strategic title combinations...\n")
    titles = generate_titles()
    
    # Add metadata
    data = add_metadata(titles)
    
    # Print statistics
    print_statistics(data)
    
    # Export files
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_filename = f"briefsy_bofu_titles_{len(data)}_{timestamp}.csv"
    json_filename = f"briefsy_bofu_titles_{len(data)}_{timestamp}.json"
    
    export_csv(data, csv_filename)
    export_json(data, json_filename)
    
    print(f"✨ Complete! Generated {len(data):,} unique, strategic titles.")
    print(f"📊 Each title targets users actively seeking newsletter solutions.")
    print(f"\nKey Differentiators Highlighted:")
    print(f"  • Multi-interest newsletters (Marketing + AI + Fitness in one)")
    print(f"  • Newsletter consolidation (replace 10+ subscriptions)")
    print(f"  • 7-agent AI system with Reddit/Twitter mining")
    print(f"  • Conversational setup (no forms)")
    print(f"  • Free forever positioning")
    print(f"  • Style matching technology")

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
Agentive AIQ BOFU Content Focus Generator

This script takes already-categorized PAA questions and generates bottom-of-funnel
content focus angles that are SEO-heavy and naturally transition to Agentive AIQ solutions.
"""

import csv
import json
import argparse
import requests
import time
import os
from typing import List, Dict
from concurrent.futures import ThreadPoolExecutor
import threading

# Configuration for VLLM API
VLLM_ENDPOINT = "http://**************:4100/v1/chat/completions"
VLLM_MODEL = "Qwen/Qwen3-4B-Instruct-2507"
VLLM_API_KEY = "this-is-my-key"
MAX_CONCURRENT = 5

def read_categorized_questions(filename: str) -> List[Dict]:
    """Read already categorized questions from CSV file."""
    questions = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # Handle different possible column names
            question_data = {}
            
            # Get the question/title
            if 'original_question' in row:
                question_data['question'] = row['original_question']
            elif 'PAA Title' in row:
                question_data['question'] = row['PAA Title']
            elif 'title' in row:
                question_data['question'] = row['title']
            else:
                question_data['question'] = list(row.values())[0]
            
            # Get parent category
            if 'parent_category' in row:
                question_data['parent_category'] = row['parent_category']
            elif 'Parent Category' in row:
                question_data['parent_category'] = row['Parent Category']
            else:
                question_data['parent_category'] = ""
            
            # Get child category
            if 'child_category' in row:
                question_data['child_category'] = row['child_category']
            elif 'Child Category' in row:
                question_data['child_category'] = row['Child Category']
            else:
                question_data['child_category'] = ""
            
            # Get existing content focus if available
            if 'content_focus' in row:
                question_data['existing_content_focus'] = row['content_focus']
            else:
                question_data['existing_content_focus'] = ""
            
            questions.append(question_data)
    return questions

def read_business_context(filename: str) -> str:
    """Read business context from text file."""
    with open(filename, 'r', encoding='utf-8') as file:
        return file.read()

def read_categories(filename: str) -> List[Dict]:
    """Read category pairs from CSV file."""
    categories = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            categories.append({
                'parent_category': row['Parent Category'],
                'child_category': row['Child Category']
            })
    return categories

def categorize_question(question: str, categories: List[Dict]) -> Dict:
    """Use LLM to categorize a question into the best matching parent/child category pair."""

    # Format categories for the prompt
    category_list = []
    for i, cat in enumerate(categories, 1):
        category_list.append(f"{i}. {cat['parent_category']} > {cat['child_category']}")

    categories_text = "\n".join(category_list)

    prompt = f"""
You are a content categorization expert. Your task is to match the given question/title to the BEST fitting category pair from the provided list.

QUESTION/TITLE: "{question}"

AVAILABLE CATEGORIES:
{categories_text}

TASK: Select the single best matching category pair that most closely aligns with the question's topic and intent.

REQUIREMENTS:
- Choose only ONE category pair (parent > child)
- Base your decision on the main topic and industry focus of the question
- Consider both the service type (parent) and specific application (child)
- If the question mentions a specific industry, prioritize industry-specific categories
- If no perfect match exists, choose the closest logical fit

Return ONLY the number of your chosen category (e.g., "15" for the 15th option).

SELECTED CATEGORY NUMBER:
"""

    response = query_vllm_api(prompt, max_tokens=50)

    # Extract the category number
    try:
        # Clean up the response to get just the number
        if "SELECTED CATEGORY NUMBER:" in response:
            category_num_str = response.split("SELECTED CATEGORY NUMBER:")[-1].strip()
        else:
            category_num_str = response.strip()

        # Extract just the number
        import re
        match = re.search(r'\d+', category_num_str)
        if match:
            category_num = int(match.group())
            if 1 <= category_num <= len(categories):
                selected_category = categories[category_num - 1]
                return {
                    'parent_category': selected_category['parent_category'],
                    'child_category': selected_category['child_category']
                }
    except (ValueError, IndexError):
        pass

    # Fallback to first category if parsing fails
    return {
        'parent_category': categories[0]['parent_category'],
        'child_category': categories[0]['child_category']
    }

def query_vllm_api(prompt: str, max_tokens: int = 800) -> str:
    """Query the VLLM API with retry logic."""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {VLLM_API_KEY}"
    }
    
    data = {
        "model": VLLM_MODEL,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.3,
        "max_tokens": max_tokens
    }
    
    max_retries = 3
    for attempt in range(max_retries):
        try:
            response = requests.post(VLLM_ENDPOINT, headers=headers, json=data, timeout=30)
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                return "Error processing request"
        except requests.exceptions.Timeout:
            if attempt < max_retries - 1:
                time.sleep(5)
                continue
            return "Error: Request timeout"
        except Exception as e:
            if attempt < max_retries - 1:
                time.sleep(3)
                continue
            return "Error connecting to VLLM"

def check_title_grammar(title: str) -> str:
    """Check and correct grammar in programmatically generated titles."""

    prompt = f"""
You are a professional editor specializing in business and technical content. Your task is to check and correct the grammar of the following title while preserving its core meaning and intent.

TITLE TO CHECK: "{title}"

REQUIREMENTS:
- Fix any grammatical errors, particularly pluralization issues
- Ensure proper article usage (a, an, the)
- Correct any awkward phrasing while maintaining the original meaning
- Keep the title concise and professional
- Do NOT change the core concept or industry focus
- Do NOT add or remove key terms like company names, technology terms, or industry-specific language
- Only make minimal corrections needed for proper grammar and readability

Return ONLY the corrected title, nothing else.

CORRECTED TITLE:
"""

    response = query_vllm_api(prompt, max_tokens=100)

    # Clean up the response to extract just the corrected title
    if "CORRECTED TITLE:" in response:
        corrected_title = response.split("CORRECTED TITLE:")[-1].strip()
    else:
        corrected_title = response.strip()

    # Remove any quotes that might have been added
    corrected_title = corrected_title.strip('"').strip("'")

    return corrected_title

def generate_bofu_content_focus(question_data: Dict, business_context: str) -> str:
    """Generate a BOFU-focused content angle for the question."""

    question = question_data['question']
    parent_category = question_data['parent_category']
    child_category = question_data['child_category']
    
    # Determine if this is an industry-specific question for tailored content
    industry_keywords = {
        'healthcare': ['medical', 'healthcare', 'health', 'dental', 'mental health'],
        'legal': ['law', 'legal', 'attorney', 'lawyer'],
        'real_estate': ['real estate', 'property management'],
        'financial': ['financial', 'investment', 'wealth management', 'banks', 'fintech'],
        'manufacturing': ['manufacturing', 'logistics', 'supply chain'],
        'professional_services': ['consulting', 'accounting', 'architecture', 'engineering'],
        'retail_ecommerce': ['retail', 'ecommerce', 'e-commerce'],
        'service_business': ['hvac', 'construction', 'roofing', 'plumbing', 'electrical', 'pest control', 'catering', 'restaurants']
    }

    detected_industry = None
    question_lower = question.lower()
    for industry, keywords in industry_keywords.items():
        if any(keyword in question_lower for keyword in keywords):
            detected_industry = industry
            break

    # Check if the title mentions specific platforms for comparison
    platform_keywords = ['zapier', 'make.com', 'n8n', 'chatgpt plus', 'chatgpt', 'make']
    mentions_platform = any(platform in question_lower for platform in platform_keywords)

    # Extract mentioned platforms
    mentioned_platforms = []
    if 'zapier' in question_lower:
        mentioned_platforms.append('Zapier')
    if 'make.com' in question_lower or 'make' in question_lower:
        mentioned_platforms.append('Make.com')
    if 'n8n' in question_lower:
        mentioned_platforms.append('n8n')
    if 'chatgpt plus' in question_lower or 'chatgpt' in question_lower:
        mentioned_platforms.append('ChatGPT Plus')

    # Create platform comparison section if platforms are mentioned
    platform_comparison_section = ""
    if mentions_platform:
        platforms_text = ", ".join(mentioned_platforms)
        platform_comparison_section = f"""
2. PLATFORM COMPARISON FOCUS (REQUIRED - platforms mentioned in title):
   - Specifically compare Agentive AIQ's no-code platform vs. {platforms_text}
   - Highlight the limitations of {platforms_text}: limited customization, complex setup, poor brand integration
   - Position Agentive AIQ as the solution with WYSIWYG editor, seamless branding, and easy deployment
   - Address why businesses need better chatbot platforms than {platforms_text} for professional customer engagement
"""
    else:
        platform_comparison_section = """
2. GENERAL PLATFORM CONSIDERATIONS:
   - Focus on the limitations of basic chatbot builders and generic solutions
   - Emphasize professional branding and customization capabilities vs. generic templates
   - Highlight ease of deployment and advanced features like two-agent architecture
"""

    prompt = f"""

You are a content strategist creating INSTRUCTIONAL research and writing briefs for AIQ Labs' BOFU content team.

BUSINESS CONTEXT:
{business_context}

QUESTION: "{question}"
CATEGORY: {parent_category} > {child_category}
DETECTED INDUSTRY: {detected_industry or 'General Business'}
PLATFORM COMPARISON NEEDED: {'Yes - ' + ', '.join(mentioned_platforms) if mentions_platform else 'No'}

TASK: Create comprehensive research and writing instructions for a BOFU article that guides decision-makers towards use Agentive AIQ

TARGET AUDIENCE: Business owners, website owners, teachers, course creators evaluating agentic chatbot systems

INSTRUCTIONAL CONTENT BRIEF:

1. RESEARCH INSTRUCTIONS FOR WRITERS:
   - Find relevant workflows and systems that could be built for {detected_industry or 'the mentioned industry'}
   - Research industry-specific website chatbot challenges
   - Look up industry ai agent / chatbot adoption rates and ROI benchmarks
   - Find case studies of successful AI implementations in similar businesses

{platform_comparison_section}

3. INDUSTRY-SPECIFIC SOLUTION EXAMPLES TO RESEARCH:
   - Identify specific operational bottlenecks in {detected_industry or 'the target industry'}
   - Find measurable outcomes: time savings, cost reduction, revenue impact

4. CONTENT STRUCTURE INSTRUCTIONS:
   - Start by acknowledging the reader's question and business context
   - Include specific examples of what AgentiveAIQ features do
   - Provide actionable research directions for decision-makers
   - Position AgentiveAIQ chatbot system as the top solution for AI Agents and custom website widgets along with the hosted pages with course creation
   - End with clear next steps (free 14 day trial no credit card required)

5. KEY MESSAGING POINTS:
   - Highlight Agentive AIQ using a 2 agent architecture
   - 9 Agent goals to choose from
   - Fully customizable chat widgets ( floating and embedded versions ) with a true WYSIWYG editor
   - Fual RAG and Graph knowledgebase system
   - Our hosted pages and course systems have long term memory-  ONLY THESE VERSIONS HAVE LONG TERM MEMORY

Write a comprehensive research and content brief (6-8 sentences) that instructs writers on how to create a BOFU article addressing this question, including specific research directions, industry examples to find, and key messaging points to emphasize.

CONTENT BRIEF:
"""

    response = query_vllm_api(prompt, max_tokens=800)
    
    # Clean up the response to extract just the content focus
    if "CONTENT FOCUS:" in response:
        content_focus = response.split("CONTENT FOCUS:")[-1].strip()
    else:
        content_focus = response.strip()
    
    return content_focus

def process_single_question(args):
    """Process a single question - for use with ThreadPoolExecutor."""
    idx, question_data, business_context, categories = args
    try:
        # First, check and correct the grammar of the title
        corrected_question = check_title_grammar(question_data['question'])

        # Categorize the question using LLM
        category_result = categorize_question(corrected_question, categories)

        # Update question_data with corrected title and categories
        corrected_question_data = question_data.copy()
        corrected_question_data['question'] = corrected_question
        corrected_question_data['parent_category'] = category_result['parent_category']
        corrected_question_data['child_category'] = category_result['child_category']

        # Generate BOFU content focus using the corrected title and categories
        new_content_focus = generate_bofu_content_focus(corrected_question_data, business_context)

        result = {
            "original_question": question_data['question'],
            "corrected_question": corrected_question,
            "parent_category": category_result['parent_category'],
            "child_category": category_result['child_category'],
            "content_focus": new_content_focus
        }
        return (idx, result)
    except Exception as e:
        error_result = {
            "original_question": question_data['question'],
            "corrected_question": question_data['question'],  # Use original if correction fails
            "parent_category": "Error",
            "child_category": "Error",
            "content_focus": f"Processing error: {str(e)}"
        }
        return (idx, error_result)

def process_questions_sequential_batches(questions: List[Dict], business_context: str, categories: List[Dict],
                                       test_mode: bool = False, concurrent: int = 5) -> List[Dict]:
    """Process questions in sequential batches with concurrent processing within each batch."""
    
    if test_mode:
        questions = questions[:20]
        print(f"Running in test mode. Processing first {len(questions)} questions...")
    else:
        print(f"Processing {len(questions)} questions with {concurrent} concurrent connections per batch...")
    
    # Create smaller batches for sequential processing
    batch_size = 500  # Process 500 questions at a time
    all_results = []
    
    for batch_start in range(0, len(questions), batch_size):
        batch_end = min(batch_start + batch_size, len(questions))
        batch_questions = questions[batch_start:batch_end]
        batch_num = (batch_start // batch_size) + 1
        total_batches = (len(questions) + batch_size - 1) // batch_size
        
        print(f"\n[PROCESSING] Batch {batch_num}/{total_batches} ({len(batch_questions)} questions)")
        
        # Prepare arguments for concurrent processing within this batch
        args_list = [
            (batch_start + i, question_data, business_context, categories)
            for i, question_data in enumerate(batch_questions)
        ]
        
        # Process this batch concurrently
        batch_results = []
        with ThreadPoolExecutor(max_workers=concurrent) as executor:
            futures = [executor.submit(process_single_question, args) for args in args_list]
            
            completed = 0
            for future in futures:
                try:
                    result = future.result()
                    batch_results.append(result)
                    completed += 1
                    
                    # Show progress every 50 questions
                    if completed % 50 == 0:
                        print(f"  [OK] {completed}/{len(batch_questions)} questions completed in batch {batch_num}")
                        
                except Exception as e:
                    print(f"  [ERROR] Error in batch {batch_num}: {e}")
        
        all_results.extend(batch_results)
        print(f"  [COMPLETE] Batch {batch_num} completed: {len(batch_results)} questions processed")
        
        # Small delay between batches to avoid overwhelming the server
        if batch_num < total_batches:
            time.sleep(2)
    
    # Sort results by original index to maintain order
    all_results.sort(key=lambda x: x[0])
    
    # Extract just the result dictionaries
    final_results = [result for _, result in all_results]
    
    return final_results

def save_results_to_csv(results: List[Dict], filename: str):
    """Save results to CSV in the requested format."""
    fieldnames = ["original_question", "corrected_question", "parent_category", "child_category", "content_focus"]

    with open(filename, 'w', newline='', encoding='utf-8') as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()

        for result in results:
            csv_row = {}
            for field in fieldnames:
                value = result.get(field)
                csv_row[field] = value if value is not None else ""
            writer.writerow(csv_row)

    print(f"Results saved to {filename}")

def main():
    """Main function to run the Agentive AIQ BOFU Content Generator."""
    parser = argparse.ArgumentParser(description="Generate BOFU content focus for already-categorized Agentive AIQ questions")
    parser.add_argument("--test", action="store_true", help="Run in test mode with first 20 questions")
    parser.add_argument("--input", default="agentiveaiq_bofu_title - agentiveaiq_bofu_titles_8061_20251004_124606.csv.csv",
                       help="Input CSV file with already categorized questions")
    parser.add_argument("--context", default="Agentive AIQ Context.txt",
                       help="Text file with business context")
    parser.add_argument("--categories", default="AIQ Labs Blog Categories - Sheet1.csv",
                       help="CSV file with category pairs")
    parser.add_argument("--output", default="agentiveaiq_bofu_content_focus.csv",
                       help="Output CSV file")
    parser.add_argument("--concurrent", type=int, default=5,
                       help="Number of concurrent connections per batch (default: 5)")

    args = parser.parse_args()

    print("=== Agentive AIQ BOFU Content Focus Generator ===\n")
    
    # Check if input files exist
    for file_path in [args.input, args.context, args.categories]:
        if not os.path.exists(file_path):
            print(f"Error: File '{file_path}' not found!")
            return

    try:
        # Read business context
        print("Loading business context...")
        business_context = read_business_context(args.context)

        # Read categories
        print("Loading categories...")
        categories = read_categories(args.categories)
        print(f"Loaded {len(categories)} category pairs")

        # Read categorized questions
        print("Loading questions...")
        questions = read_categorized_questions(args.input)
        print(f"Loaded {len(questions)} questions to process")

        # Process questions with sequential batch processing
        start_time = time.time()
        results = process_questions_sequential_batches(questions, business_context, categories, args.test, args.concurrent)
        end_time = time.time()
        
        # Save results
        save_results_to_csv(results, args.output)
        
        # Print summary
        processing_time = end_time - start_time
        
        print(f"\n=== SUMMARY ===")
        print(f"Total questions processed: {len(results)}")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Average time per question: {processing_time/len(results):.2f} seconds")
        print(f"\nBOFU content focus saved to: {args.output}")
        
    except Exception as e:
        print(f"Error during processing: {e}")
        return

if __name__ == "__main__":
    main()